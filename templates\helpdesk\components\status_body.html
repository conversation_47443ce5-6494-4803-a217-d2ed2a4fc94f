<div class="status-body">
    <div class="row g-3">
        <!-- Appeal Processing Section -->
        {% if ticket.request_type == 'appeal' and (ticket.appeal_type == 'hidden_journey' or
        ticket.appeal_type == 'sharing_block' or ticket.appeal_type == 'ban') and can_manage_content() %}
        {% if ticket.assigned_to and ticket.status in ['new', 'open'] and ticket.user_id != session.user_id
        and ticket.assigned_to != session.user_id %}
        <div class="rounded-3">
            <div class="d-flex align-items-center">
                <div>
                    <h6 class="fw-bold mb-1 text-primary">Appeal Assigned</h6>
                    <p class="mb-0 small">This appeal is assigned to {{ ticket.get('assigned_username',
                        ticket.staff_name) }}. Only the assigned staff member can process this appeal.
                    </p>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="appeal-actions-section">
            {% include 'helpdesk/components/appeal_actions.html' %}
        </div>
        <div class="col-lg-5 col-md-5 mb-3">
            {% if ticket.status in ['new', 'open'] %}
            {% elif ticket.status == 'approved' %}
            <h6 class="text-uppercase text-muted small fw-bold"><i
                    class="bi bi-info-circle text-dark me-1"></i>Result
            </h6>
            <div class="rounded-3">
                <div class="d-flex align-items-start">
                    <div>
                        <h6 class="fw-bold mb-1 text-success">Appeal Approved</h6>
                        <p class="mb-0 small">This appeal has been approved and the journey has been
                            unhidden.</p>
                    </div>
                </div>
            </div>
            {% elif ticket.status == 'rejected' %}
            <h6 class="text-uppercase text-muted small fw-bold"><i
                    class="bi bi-info-circle text-success me-1"></i>Result
            </h6>
            <div class="rounded-3">
                <div class="d-flex align-items-start">
                    <div>
                        <h6 class="fw-bold mb-1 text-danger">Appeal Rejected</h6>
                        <p class="mb-0 small">This appeal has been reviewed and rejected.</p>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Reason -->
        {% if ticket.request_type == 'appeal' and ticket.admin_response %}
        <div class="col-lg-7 col-md-7">
            <h6 class="text-uppercase text-muted small fw-bold">
                {% if ticket.status == 'approved' %}
                <i class="bi bi-check-circle text-success me-1"></i>Staff Response
                {% elif ticket.status == 'rejected' %}
                <i class="bi bi-x-circle text-danger me-1"></i>Rejection Reason
                {% else %}
                <i class="bi bi-chat-square-text me-1"></i>Staff Response
                {% endif %}
            </h6>
            <div class="rounded-3"
                style="max-height: calc(100vh - 750px); overflow-y: auto; overflow-x: hidden; max-width: 100%; white-space: normal; word-break: break-word; padding: 0; margin: 0;">
                <p class="mb-0" style="margin:0; padding:0;">
                    {{ ticket.admin_response }}
                </p>
            </div>
        </div>
        {% endif %}
        {% endif %}
    </div>
    {% if ticket.status == 'stalled' %}
    <div class="rounded-3 mt-3 mb-3">
        <div class="d-flex align-items-start">
            <div>
                <h6 class="fw-bold mb-1 text-warning">Ticket Stalled</h6>
                <p class="mb-0 small">This ticket has been stalled.</p>
            </div>
        </div>
    </div>
    {% elif ticket.status == 'resolved' %}
    <div class="rounded-3 mt-3 mb-3">
        <div class="d-flex align-items-start">
            <div>
                <h6 class="fw-bold mb-1 text-success">Ticket Resolved</h6>
                <p class="mb-0 small">This ticket has been reviewed and resolved.</p>
            </div>
        </div>
    </div>
    {% elif ticket.status == 'new' %}
    <div class="rounded-3 mt-3 mb-3">
        <div class="d-flex align-items-start">
            <div>
                <h6 class="fw-bold mb-1 text-success">Ticket New</h6>
                <p class="mb-0 small">This ticket is currently new and waiting for a staff member to
                    process it. Use "Take Ticket" or "Assign Ticket" above.</p>
            </div>
        </div>
    </div>
    {% elif ticket.status == 'open' and ticket.request_type != 'appeal' %}
    {% if not ticket.assigned_to %}
    <div class="rounded-3 mt-3 mb-3">
        <div class="d-flex align-items-start">
            <div>
                <h6 class="fw-bold mb-1 text-success">Ticket Open</h6>
                <p class="mb-0 small">This ticket is currently open and waiting for a staff member to
                    process it. Use "Take Ticket" or "Assign Ticket" above.</p>
            </div>
        </div>
    </div>
    {% else %}
    <div class="rounded-3 mt-3 mb-3">
        <div class="d-flex align-items-start">
            <div>
                <h6 class="fw-bold mb-1 text-success">Ticket Open</h6>
                <p class="mb-0 small">This ticket is currently assigned to {{
                    ticket.get('assigned_username',
                    ticket.staff_name) }}.</p>
            </div>
        </div>
    </div>
    {% endif %}
    {% endif %}
</div>
